#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI记忆知识管理系统
用于系统化地积累、组织和检索项目开发中的模式、经验和最佳实践

功能:
- 设计模式知识库管理
- 项目历史记录
- 最佳实践积累
- 智能检索和推荐
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import defaultdict
import hashlib

@dataclass
class PatternRecord:
    """设计模式记录"""
    pattern_id: str
    name: str
    category: str
    description: str
    use_cases: List[str]
    implementation: Dict[str, Any]
    pros: List[str]
    cons: List[str]
    related_patterns: List[str]
    examples: List[str]
    tags: List[str]
    created_at: str
    updated_at: str
    usage_count: int = 0

@dataclass
class ProjectRecord:
    """项目记录"""
    project_id: str
    name: str
    description: str
    technologies: List[str]
    patterns_used: List[str]
    challenges: List[str]
    solutions: List[str]
    lessons_learned: List[str]
    metrics: Dict[str, Any]
    created_at: str
    completed_at: Optional[str] = None

@dataclass
class BestPractice:
    """最佳实践记录"""
    practice_id: str
    title: str
    category: str
    description: str
    context: str
    implementation: str
    benefits: List[str]
    pitfalls: List[str]
    related_patterns: List[str]
    examples: List[str]
    confidence_score: float
    tags: List[str]
    created_at: str

class KnowledgeManager:
    """知识管理器"""
    
    def __init__(self, knowledge_base_path: str = "ai_memory"):
        self.base_path = Path(knowledge_base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个数据库
        self.patterns_db_path = self.base_path / "patterns_db"
        self.projects_db_path = self.base_path / "project_history"
        self.practices_db_path = self.base_path / "best_practices"
        
        for path in [self.patterns_db_path, self.projects_db_path, self.practices_db_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        # 加载索引
        self.patterns_index = self._load_index("patterns_index.json")
        self.projects_index = self._load_index("projects_index.json")
        self.practices_index = self._load_index("practices_index.json")
        
        # 标签索引
        self.tag_index = self._build_tag_index()
    
    def _load_index(self, index_name: str) -> Dict[str, Any]:
        """加载索引文件"""
        index_path = self.base_path / index_name
        if index_path.exists():
            with open(index_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _save_index(self, index_name: str, index_data: Dict[str, Any]):
        """保存索引文件"""
        index_path = self.base_path / index_name
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
    
    def _build_tag_index(self) -> Dict[str, Set[str]]:
        """构建标签索引"""
        tag_index = defaultdict(set)
        
        # 索引模式标签
        for pattern_id, pattern_info in self.patterns_index.items():
            for tag in pattern_info.get('tags', []):
                tag_index[tag].add(f"pattern:{pattern_id}")
        
        # 索引项目标签
        for project_id, project_info in self.projects_index.items():
            for tech in project_info.get('technologies', []):
                tag_index[tech].add(f"project:{project_id}")
        
        # 索引实践标签
        for practice_id, practice_info in self.practices_index.items():
            for tag in practice_info.get('tags', []):
                tag_index[tag].add(f"practice:{practice_id}")
        
        return {k: list(v) for k, v in tag_index.items()}
    
    def add_pattern(self, pattern: PatternRecord) -> str:
        """添加设计模式"""
        pattern_file = self.patterns_db_path / f"{pattern.pattern_id}.yaml"
        
        with open(pattern_file, 'w', encoding='utf-8') as f:
            yaml.dump(asdict(pattern), f, allow_unicode=True, default_flow_style=False)
        
        # 更新索引
        self.patterns_index[pattern.pattern_id] = {
            'name': pattern.name,
            'category': pattern.category,
            'tags': pattern.tags,
            'created_at': pattern.created_at,
            'usage_count': pattern.usage_count
        }
        self._save_index("patterns_index.json", self.patterns_index)
        
        # 更新标签索引
        self.tag_index = self._build_tag_index()
        
        return pattern.pattern_id
    
    def add_project(self, project: ProjectRecord) -> str:
        """添加项目记录"""
        project_file = self.projects_db_path / f"{project.project_id}.yaml"
        
        with open(project_file, 'w', encoding='utf-8') as f:
            yaml.dump(asdict(project), f, allow_unicode=True, default_flow_style=False)
        
        # 更新索引
        self.projects_index[project.project_id] = {
            'name': project.name,
            'technologies': project.technologies,
            'patterns_used': project.patterns_used,
            'created_at': project.created_at,
            'completed_at': project.completed_at
        }
        self._save_index("projects_index.json", self.projects_index)
        
        # 更新模式使用计数
        for pattern_id in project.patterns_used:
            self._increment_pattern_usage(pattern_id)
        
        return project.project_id
    
    def add_best_practice(self, practice: BestPractice) -> str:
        """添加最佳实践"""
        practice_file = self.practices_db_path / f"{practice.practice_id}.yaml"
        
        with open(practice_file, 'w', encoding='utf-8') as f:
            yaml.dump(asdict(practice), f, allow_unicode=True, default_flow_style=False)
        
        # 更新索引
        self.practices_index[practice.practice_id] = {
            'title': practice.title,
            'category': practice.category,
            'tags': practice.tags,
            'confidence_score': practice.confidence_score,
            'created_at': practice.created_at
        }
        self._save_index("practices_index.json", self.practices_index)
        
        return practice.practice_id
    
    def _increment_pattern_usage(self, pattern_id: str):
        """增加模式使用计数"""
        pattern_file = self.patterns_db_path / f"{pattern_id}.yaml"
        if pattern_file.exists():
            with open(pattern_file, 'r', encoding='utf-8') as f:
                pattern_data = yaml.safe_load(f)
            
            pattern_data['usage_count'] = pattern_data.get('usage_count', 0) + 1
            pattern_data['updated_at'] = datetime.now().isoformat()
            
            with open(pattern_file, 'w', encoding='utf-8') as f:
                yaml.dump(pattern_data, f, allow_unicode=True, default_flow_style=False)
            
            # 更新索引
            self.patterns_index[pattern_id]['usage_count'] = pattern_data['usage_count']
            self._save_index("patterns_index.json", self.patterns_index)
    
    def search_patterns(self, query: str = "", category: str = "", tags: List[str] = None) -> List[PatternRecord]:
        """搜索设计模式"""
        results = []
        tags = tags or []
        
        for pattern_id, pattern_info in self.patterns_index.items():
            # 类别过滤
            if category and pattern_info.get('category') != category:
                continue
            
            # 标签过滤
            if tags and not any(tag in pattern_info.get('tags', []) for tag in tags):
                continue
            
            # 文本搜索
            if query:
                searchable_text = f"{pattern_info.get('name', '')} {pattern_info.get('category', '')}"
                if query.lower() not in searchable_text.lower():
                    continue
            
            # 加载完整记录
            pattern_file = self.patterns_db_path / f"{pattern_id}.yaml"
            if pattern_file.exists():
                with open(pattern_file, 'r', encoding='utf-8') as f:
                    pattern_data = yaml.safe_load(f)
                results.append(PatternRecord(**pattern_data))
        
        # 按使用频率排序
        results.sort(key=lambda x: x.usage_count, reverse=True)
        return results
    
    def recommend_patterns(self, context: Dict[str, Any]) -> List[PatternRecord]:
        """基于上下文推荐设计模式"""
        recommendations = []
        
        # 基于技术栈推荐
        technologies = context.get('technologies', [])
        for tech in technologies:
            if tech in self.tag_index:
                for item_id in self.tag_index[tech]:
                    if item_id.startswith('pattern:'):
                        pattern_id = item_id.split(':', 1)[1]
                        pattern = self._load_pattern(pattern_id)
                        if pattern:
                            recommendations.append(pattern)
        
        # 基于问题类型推荐
        problem_type = context.get('problem_type', '')
        if problem_type:
            patterns = self.search_patterns(query=problem_type)
            recommendations.extend(patterns)
        
        # 去重并按使用频率排序
        seen = set()
        unique_recommendations = []
        for pattern in recommendations:
            if pattern.pattern_id not in seen:
                seen.add(pattern.pattern_id)
                unique_recommendations.append(pattern)
        
        unique_recommendations.sort(key=lambda x: x.usage_count, reverse=True)
        return unique_recommendations[:5]  # 返回前5个推荐
    
    def _load_pattern(self, pattern_id: str) -> Optional[PatternRecord]:
        """加载模式记录"""
        pattern_file = self.patterns_db_path / f"{pattern_id}.yaml"
        if pattern_file.exists():
            with open(pattern_file, 'r', encoding='utf-8') as f:
                pattern_data = yaml.safe_load(f)
            return PatternRecord(**pattern_data)
        return None
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """生成知识库总结报告"""
        return {
            'patterns_count': len(self.patterns_index),
            'projects_count': len(self.projects_index),
            'practices_count': len(self.practices_index),
            'top_patterns': self._get_top_patterns(5),
            'popular_technologies': self._get_popular_technologies(10),
            'recent_projects': self._get_recent_projects(5),
            'knowledge_coverage': self._calculate_coverage(),
            'generated_at': datetime.now().isoformat()
        }
    
    def _get_top_patterns(self, limit: int) -> List[Dict[str, Any]]:
        """获取最受欢迎的模式"""
        patterns = [(pid, info) for pid, info in self.patterns_index.items()]
        patterns.sort(key=lambda x: x[1].get('usage_count', 0), reverse=True)
        return [{'id': pid, 'name': info['name'], 'usage_count': info.get('usage_count', 0)} 
                for pid, info in patterns[:limit]]
    
    def _get_popular_technologies(self, limit: int) -> List[Dict[str, Any]]:
        """获取热门技术"""
        tech_count = defaultdict(int)
        for project_info in self.projects_index.values():
            for tech in project_info.get('technologies', []):
                tech_count[tech] += 1
        
        sorted_techs = sorted(tech_count.items(), key=lambda x: x[1], reverse=True)
        return [{'technology': tech, 'project_count': count} 
                for tech, count in sorted_techs[:limit]]
    
    def _get_recent_projects(self, limit: int) -> List[Dict[str, Any]]:
        """获取最近项目"""
        projects = [(pid, info) for pid, info in self.projects_index.items()]
        projects.sort(key=lambda x: x[1].get('created_at', ''), reverse=True)
        return [{'id': pid, 'name': info['name'], 'created_at': info.get('created_at')} 
                for pid, info in projects[:limit]]
    
    def _calculate_coverage(self) -> Dict[str, float]:
        """计算知识覆盖度"""
        total_categories = len(set(info.get('category', '') for info in self.patterns_index.values()))
        total_technologies = len(set(tech for info in self.projects_index.values() 
                                   for tech in info.get('technologies', [])))
        
        return {
            'pattern_categories': total_categories,
            'technology_coverage': total_technologies,
            'practice_categories': len(set(info.get('category', '') for info in self.practices_index.values()))
        }

# 使用示例
if __name__ == "__main__":
    km = KnowledgeManager()
    
    # 添加示例模式
    pattern = PatternRecord(
        pattern_id="strategy_pattern_001",
        name="策略模式",
        category="行为型模式",
        description="定义一系列算法，把它们一个个封装起来，并且使它们可相互替换",
        use_cases=["算法选择", "行为变化", "条件分支优化"],
        implementation={"language": "Python", "complexity": "Medium"},
        pros=["易于扩展", "符合开闭原则", "避免条件分支"],
        cons=["增加类的数量", "客户端需要了解策略"],
        related_patterns=["状态模式", "模板方法模式"],
        examples=["lyric_timeline.py"],
        tags=["OOP", "设计模式", "Python"],
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat()
    )
    
    km.add_pattern(pattern)
    print("知识库初始化完成！")
