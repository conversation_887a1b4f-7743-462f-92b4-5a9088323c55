# LyricTimeline项目脚手架配置示例
# 基于我们刚完成的OOP重构项目

project_name: "LyricTimeline"
module_name: "lyric_timeline"
description: "歌词时间轴OOP重构项目，实现策略模式的歌词显示系统"
author: "AI Assistant"
template_type: "strategy_pattern"
output_path: "./generated/lyric_timeline"

parameters:
  # 策略模式配置
  enum_name: "LyricDisplayMode"
  enum_description: "歌词显示模式枚举"
  enum_values: |
    SIMPLE_FADE = "simple_fade"           # 简单淡入淡出
    ENHANCED_PREVIEW = "enhanced_preview"  # 增强模式：当前+预览
    BILINGUAL_SYNC = "bilingual_sync"     # 双语同步显示
    KARAOKE_STYLE = "karaoke_style"       # 卡拉OK样式（未来扩展）

  # 数据类配置
  data_class_name: "LyricRect"
  data_class_description: "歌词显示区域信息"
  data_class_fields: |
    x: int
    y: int
    width: int
    height: int
  
  config_class_name: "LyricStyle"
  config_class_description: "歌词样式配置"
  config_fields: |
    font_size: int = 80
    font_color: str = 'white'
    highlight_color: str = '#FFD700'
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 200)
    glow_enabled: bool = False
    animation_style: str = 'fade'

  # 策略接口配置
  strategy_interface_name: "LyricDisplayStrategy"
  strategy_interface_description: "歌词显示策略抽象基类"
  
  strategy_method_1: "calculate_required_rect"
  strategy_method_1_params: "video_width: int, video_height: int"
  strategy_method_1_return: "LyricRect"
  strategy_method_1_description: "计算所需的显示区域"
  
  strategy_method_2: "generate_clips"
  strategy_method_2_params: "generator: Any, duration: float"
  strategy_method_2_return: "List[ImageClip]"
  strategy_method_2_description: "生成歌词视频片段"

  # 具体策略配置
  strategy_1_name: "SimpleFadeStrategy"
  strategy_1_description: "简单淡入淡出显示策略"
  strategy_1_init_params: "y_position: Optional[int] = None, is_highlighted: bool = True"
  
  strategy_2_name: "EnhancedPreviewStrategy"
  strategy_2_description: "增强预览模式：当前歌词+下一句预览"
  strategy_2_init_params: "current_y_offset: int = -50, preview_y_offset: int = 80"

  # 上下文类配置
  context_class_name: "LyricTimeline"
  context_class_description: "歌词时间轴类 - 封装歌词数据和显示逻辑"
  context_init_params: |
    lyrics_data: List[Tuple[float, str]],
    language: str = "unknown",
    style: Optional[LyricStyle] = None,
    display_mode: LyricDisplayMode = LyricDisplayMode.SIMPLE_FADE
  
  context_method_1: "calculate_required_rect"
  context_method_1_params: "video_width: int, video_height: int"
  context_method_1_return: "LyricRect"
  context_method_1_description: "计算所需的显示区域"
  
  context_method_2: "generate_clips"
  context_method_2_params: "generator: Any, duration: float"
  context_method_2_return: "List[ImageClip]"
  context_method_2_description: "生成视频片段"

  # 工厂方法配置
  factory_method_1: "enhanced_timeline"
  factory_1_description: "创建增强预览模式的时间轴"
  factory_1_params: "lyrics_data: List[Tuple[float, str]], language: str = 'chinese'"
  
  factory_method_2: "simple_timeline"
  factory_2_description: "创建简单淡入淡出模式的时间轴"
  factory_2_params: "lyrics_data: List[Tuple[float, str]], language: str = 'english'"

  # 示例数据
  example_data: |
    test_lyrics = [
        (0.0, "第一句歌词"),
        (3.0, "第二句歌词"),
        (6.0, "第三句歌词"),
        (9.0, "第四句歌词")
    ]

  # 使用示例
  example_usage: |
    # 创建增强预览模式时间轴
    enhanced_timeline = create_enhanced_timeline(test_lyrics, "chinese")
    print(f"时间轴信息: {enhanced_timeline.get_info()}")
    
    # 创建简单模式时间轴
    simple_timeline = create_simple_timeline(test_lyrics, "english")
    print(f"时间轴信息: {simple_timeline.get_info()}")
    
    # 计算所需区域
    video_width, video_height = 1280, 720
    enhanced_rect = enhanced_timeline.calculate_required_rect(video_width, video_height)
    simple_rect = simple_timeline.calculate_required_rect(video_width, video_height)
    
    print(f"增强模式所需区域: {enhanced_rect}")
    print(f"简单模式所需区域: {simple_rect}")

# 测试配置
test_config:
  test_name: "LyricTimeline OOP重构测试"
  test_class_name: "TestLyricTimeline"
  test_class_description: "LyricTimeline功能测试类"
  timeout: 60
  verbose: true
  test_data_path: "./test_data"
  output_path: "./test_output"
  
  test_methods:
    - name: "basic_functionality"
      description: "测试基本功能"
    - name: "strategy_switching"
      description: "测试策略切换"
    - name: "rect_calculation"
      description: "测试区域计算"
    - name: "lrc_parsing"
      description: "测试LRC文件解析"

# 文档配置
documentation_config:
  refactor_type: "OOP策略模式重构"
  start_date: "2024-01-01"
  end_date: "2024-01-15"
  responsible_person: "AI Assistant"
  affected_files_count: 5
  code_lines_change: "+500/-200"
  new_features_count: 3
  removed_features_count: 1
  
  major_changes:
    - "实现策略模式的歌词显示系统"
    - "移除旧版向后兼容代码"
    - "添加自报告尺寸机制"
    - "统一OOP接口设计"
  
  technical_tags: ["Python", "OOP", "策略模式", "重构"]
  pattern_tags: ["Strategy", "Factory", "Template"]
  complexity_level: "Medium"
  impact_scope: "Module"
