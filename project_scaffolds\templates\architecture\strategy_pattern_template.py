# -*- coding: utf-8 -*-
"""
{{MODULE_NAME}} 策略模式实现模板
{{DESCRIPTION}}

生成时间: {{GENERATION_TIME}}
项目: {{PROJECT_NAME}}
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# ============================================================================
# 基础数据结构
# ============================================================================

class {{ENUM_NAME}}(Enum):
    """{{ENUM_DESCRIPTION}}"""
    {{ENUM_VALUES}}

@dataclass
class {{DATA_CLASS_NAME}}:
    """{{DATA_CLASS_DESCRIPTION}}"""
    {{DATA_CLASS_FIELDS}}
    
    def __post_init__(self):
        """数据验证"""
        {{VALIDATION_LOGIC}}
    
    def copy(self) -> '{{DATA_CLASS_NAME}}':
        """创建副本"""
        return {{DATA_CLASS_NAME}}(
            {{COPY_FIELDS}}
        )

@dataclass
class {{CONFIG_CLASS_NAME}}:
    """{{CONFIG_CLASS_DESCRIPTION}}"""
    {{CONFIG_FIELDS}}
    
    def copy(self) -> '{{CONFIG_CLASS_NAME}}':
        """创建配置副本"""
        return {{CONFIG_CLASS_NAME}}(
            {{CONFIG_COPY_FIELDS}}
        )

# ============================================================================
# 策略模式实现
# ============================================================================

class {{STRATEGY_INTERFACE_NAME}}(ABC):
    """{{STRATEGY_INTERFACE_DESCRIPTION}}"""

    @abstractmethod
    def {{STRATEGY_METHOD_1}}(self, context: '{{CONTEXT_CLASS_NAME}}', 
                              {{STRATEGY_METHOD_1_PARAMS}}) -> {{STRATEGY_METHOD_1_RETURN}}:
        """{{STRATEGY_METHOD_1_DESCRIPTION}}"""
        pass

    @abstractmethod
    def {{STRATEGY_METHOD_2}}(self, context: '{{CONTEXT_CLASS_NAME}}',
                              {{STRATEGY_METHOD_2_PARAMS}}) -> {{STRATEGY_METHOD_2_RETURN}}:
        """{{STRATEGY_METHOD_2_DESCRIPTION}}"""
        pass

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            "strategy_type": self.__class__.__name__,
            "parameters": self.__dict__.copy()
        }

class {{STRATEGY_1_NAME}}({{STRATEGY_INTERFACE_NAME}}):
    """{{STRATEGY_1_DESCRIPTION}}"""

    def __init__(self, {{STRATEGY_1_INIT_PARAMS}}):
        {{STRATEGY_1_INIT_LOGIC}}

    def {{STRATEGY_METHOD_1}}(self, context: '{{CONTEXT_CLASS_NAME}}',
                              {{STRATEGY_METHOD_1_PARAMS}}) -> {{STRATEGY_METHOD_1_RETURN}}:
        """{{STRATEGY_1_METHOD_1_DESCRIPTION}}"""
        {{STRATEGY_1_METHOD_1_LOGIC}}

    def {{STRATEGY_METHOD_2}}(self, context: '{{CONTEXT_CLASS_NAME}}',
                              {{STRATEGY_METHOD_2_PARAMS}}) -> {{STRATEGY_METHOD_2_RETURN}}:
        """{{STRATEGY_1_METHOD_2_DESCRIPTION}}"""
        {{STRATEGY_1_METHOD_2_LOGIC}}

class {{STRATEGY_2_NAME}}({{STRATEGY_INTERFACE_NAME}}):
    """{{STRATEGY_2_DESCRIPTION}}"""

    def __init__(self, {{STRATEGY_2_INIT_PARAMS}}):
        {{STRATEGY_2_INIT_LOGIC}}

    def {{STRATEGY_METHOD_1}}(self, context: '{{CONTEXT_CLASS_NAME}}',
                              {{STRATEGY_METHOD_1_PARAMS}}) -> {{STRATEGY_METHOD_1_RETURN}}:
        """{{STRATEGY_2_METHOD_1_DESCRIPTION}}"""
        {{STRATEGY_2_METHOD_1_LOGIC}}

    def {{STRATEGY_METHOD_2}}(self, context: '{{CONTEXT_CLASS_NAME}}',
                              {{STRATEGY_METHOD_2_PARAMS}}) -> {{STRATEGY_METHOD_2_RETURN}}:
        """{{STRATEGY_2_METHOD_2_DESCRIPTION}}"""
        {{STRATEGY_2_METHOD_2_LOGIC}}

# ============================================================================
# 主要的上下文类
# ============================================================================

class {{CONTEXT_CLASS_NAME}}:
    """{{CONTEXT_CLASS_DESCRIPTION}}"""

    def __init__(self, {{CONTEXT_INIT_PARAMS}}):
        """初始化上下文"""
        {{CONTEXT_INIT_LOGIC}}
        self._strategy: Optional[{{STRATEGY_INTERFACE_NAME}}] = None
        self._setup_strategy()

    def _setup_strategy(self):
        """根据模式设置策略"""
        {{STRATEGY_SETUP_LOGIC}}

    def set_strategy(self, strategy_type: {{ENUM_NAME}}, **kwargs):
        """设置策略"""
        {{STRATEGY_SETTER_LOGIC}}

    def {{CONTEXT_METHOD_1}}(self, {{CONTEXT_METHOD_1_PARAMS}}) -> {{CONTEXT_METHOD_1_RETURN}}:
        """{{CONTEXT_METHOD_1_DESCRIPTION}}"""
        if not self._strategy:
            raise ValueError("策略未设置")
        return self._strategy.{{STRATEGY_METHOD_1}}(self, {{CONTEXT_METHOD_1_ARGS}})

    def {{CONTEXT_METHOD_2}}(self, {{CONTEXT_METHOD_2_PARAMS}}) -> {{CONTEXT_METHOD_2_RETURN}}:
        """{{CONTEXT_METHOD_2_DESCRIPTION}}"""
        if not self._strategy:
            raise ValueError("策略未设置")
        return self._strategy.{{STRATEGY_METHOD_2}}(self, {{CONTEXT_METHOD_2_ARGS}})

    def get_info(self) -> Dict[str, Any]:
        """获取上下文信息"""
        return {
            {{CONTEXT_INFO_FIELDS}},
            "strategy_info": self._strategy.get_strategy_info() if self._strategy else None
        }

    @classmethod
    def from_{{SOURCE_TYPE}}(cls, {{FROM_SOURCE_PARAMS}}) -> '{{CONTEXT_CLASS_NAME}}':
        """从{{SOURCE_TYPE}}创建上下文"""
        {{FROM_SOURCE_LOGIC}}

# ============================================================================
# 便捷函数和工厂方法
# ============================================================================

def create_{{FACTORY_METHOD_1}}({{FACTORY_1_PARAMS}}) -> {{CONTEXT_CLASS_NAME}}:
    """{{FACTORY_1_DESCRIPTION}}"""
    {{FACTORY_1_LOGIC}}

def create_{{FACTORY_METHOD_2}}({{FACTORY_2_PARAMS}}) -> {{CONTEXT_CLASS_NAME}}:
    """{{FACTORY_2_DESCRIPTION}}"""
    {{FACTORY_2_LOGIC}}

# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    print("{{MODULE_NAME}} 策略模式演示")
    print("=" * 50)

    # 示例数据
    {{EXAMPLE_DATA}}

    # 创建上下文
    {{EXAMPLE_USAGE}}

    print("策略模式演示完成！")
