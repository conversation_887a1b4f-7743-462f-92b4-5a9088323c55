#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚手架代码生成器
基于模板和配置自动生成项目脚手架代码

功能:
- 模板参数化替换
- 多种代码模式生成
- 自动化文档生成
- 最佳实践应用
"""

import os
import re
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from jinja2 import Environment, FileSystemLoader, Template

@dataclass
class ScaffoldConfig:
    """脚手架配置"""
    project_name: str
    module_name: str
    description: str
    author: str
    template_type: str
    output_path: str
    parameters: Dict[str, Any]
    
    @classmethod
    def from_file(cls, config_path: str) -> 'ScaffoldConfig':
        """从配置文件加载"""
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                data = yaml.safe_load(f)
            else:
                data = json.load(f)
        return cls(**data)

class TemplateEngine:
    """模板引擎"""
    
    def __init__(self, templates_dir: str):
        self.templates_dir = Path(templates_dir)
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 注册自定义过滤器
        self.env.filters['snake_case'] = self._to_snake_case
        self.env.filters['camel_case'] = self._to_camel_case
        self.env.filters['pascal_case'] = self._to_pascal_case
    
    def _to_snake_case(self, text: str) -> str:
        """转换为snake_case"""
        return re.sub(r'(?<!^)(?=[A-Z])', '_', text).lower()
    
    def _to_camel_case(self, text: str) -> str:
        """转换为camelCase"""
        components = text.split('_')
        return components[0] + ''.join(x.title() for x in components[1:])
    
    def _to_pascal_case(self, text: str) -> str:
        """转换为PascalCase"""
        return ''.join(x.title() for x in text.split('_'))
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """渲染模板"""
        template = self.env.get_template(template_name)
        return template.render(**context)
    
    def render_string(self, template_string: str, context: Dict[str, Any]) -> str:
        """渲染字符串模板"""
        template = Template(template_string)
        return template.render(**context)

class ScaffoldGenerator:
    """脚手架生成器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = Path(templates_dir)
        self.engine = TemplateEngine(templates_dir)
        self.patterns = self._load_patterns()
    
    def _load_patterns(self) -> Dict[str, Any]:
        """加载设计模式配置"""
        patterns_file = self.templates_dir.parent / "patterns" / "patterns.yaml"
        if patterns_file.exists():
            with open(patterns_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        return {}
    
    def generate_from_config(self, config: ScaffoldConfig) -> List[str]:
        """根据配置生成脚手架"""
        generated_files = []
        
        # 准备上下文
        context = self._prepare_context(config)
        
        # 根据模板类型生成
        if config.template_type == "strategy_pattern":
            generated_files.extend(self._generate_strategy_pattern(config, context))
        elif config.template_type == "test_suite":
            generated_files.extend(self._generate_test_suite(config, context))
        elif config.template_type == "oop_refactor":
            generated_files.extend(self._generate_oop_refactor(config, context))
        elif config.template_type == "documentation":
            generated_files.extend(self._generate_documentation(config, context))
        else:
            raise ValueError(f"不支持的模板类型: {config.template_type}")
        
        return generated_files
    
    def _prepare_context(self, config: ScaffoldConfig) -> Dict[str, Any]:
        """准备模板上下文"""
        context = asdict(config)
        context.update({
            'GENERATION_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'GENERATOR_NAME': 'ScaffoldGenerator v1.0',
            'YEAR': datetime.now().year,
        })
        
        # 添加命名转换
        context.update({
            'MODULE_NAME_SNAKE': self.engine._to_snake_case(config.module_name),
            'MODULE_NAME_CAMEL': self.engine._to_camel_case(config.module_name),
            'MODULE_NAME_PASCAL': self.engine._to_pascal_case(config.module_name),
        })
        
        return context
    
    def _generate_strategy_pattern(self, config: ScaffoldConfig, context: Dict[str, Any]) -> List[str]:
        """生成策略模式代码"""
        generated_files = []
        output_dir = Path(config.output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成主模块
        main_file = output_dir / f"{config.module_name}.py"
        content = self.engine.render_template("architecture/strategy_pattern_template.py", context)
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(main_file))
        
        # 生成测试文件
        test_file = output_dir / f"test_{config.module_name}.py"
        test_context = context.copy()
        test_context.update({
            'TEST_NAME': f"{config.module_name} 策略模式测试",
            'TEST_CLASS_NAME': f"Test{self.engine._to_pascal_case(config.module_name)}",
        })
        content = self.engine.render_template("testing/test_template.py", test_context)
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(test_file))
        
        return generated_files
    
    def _generate_test_suite(self, config: ScaffoldConfig, context: Dict[str, Any]) -> List[str]:
        """生成测试套件"""
        generated_files = []
        output_dir = Path(config.output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成不同类型的测试
        test_types = ['unit', 'integration', 'demo']
        
        for test_type in test_types:
            test_file = output_dir / f"{test_type}_test_{config.module_name}.py"
            test_context = context.copy()
            test_context.update({
                'TEST_NAME': f"{config.module_name} {test_type.title()} 测试",
                'TEST_CLASS_NAME': f"{test_type.title()}Test{self.engine._to_pascal_case(config.module_name)}",
                'TEST_TYPE': test_type,
            })
            
            content = self.engine.render_template("testing/test_template.py", test_context)
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(content)
            generated_files.append(str(test_file))
        
        return generated_files
    
    def _generate_oop_refactor(self, config: ScaffoldConfig, context: Dict[str, Any]) -> List[str]:
        """生成OOP重构代码"""
        generated_files = []
        output_dir = Path(config.output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成重构后的主模块
        refactored_file = output_dir / f"{config.module_name}_refactored.py"
        content = self.engine.render_template("architecture/oop_refactor_template.py", context)
        with open(refactored_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(refactored_file))
        
        # 生成迁移脚本
        migration_file = output_dir / f"migrate_to_{config.module_name}.py"
        migration_context = context.copy()
        migration_context.update({
            'MIGRATION_NAME': f"迁移到{config.module_name}",
            'OLD_MODULE': config.parameters.get('old_module', 'old_module'),
            'NEW_MODULE': config.module_name,
        })
        content = self.engine.render_template("migration/migration_template.py", migration_context)
        with open(migration_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(migration_file))
        
        return generated_files
    
    def _generate_documentation(self, config: ScaffoldConfig, context: Dict[str, Any]) -> List[str]:
        """生成文档"""
        generated_files = []
        output_dir = Path(config.output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成重构总结
        summary_file = output_dir / f"{config.module_name}_refactor_summary.md"
        content = self.engine.render_template("documentation/refactor_summary_template.md", context)
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(summary_file))
        
        # 生成API文档
        api_file = output_dir / f"{config.module_name}_api.md"
        api_context = context.copy()
        api_context.update({
            'API_VERSION': config.parameters.get('api_version', '1.0'),
            'API_BASE_URL': config.parameters.get('api_base_url', ''),
        })
        content = self.engine.render_template("documentation/api_doc_template.md", api_context)
        with open(api_file, 'w', encoding='utf-8') as f:
            f.write(content)
        generated_files.append(str(api_file))
        
        return generated_files

def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='脚手架代码生成器')
    parser.add_argument('config', help='配置文件路径')
    parser.add_argument('--templates-dir', default='templates', help='模板目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 加载配置
    config = ScaffoldConfig.from_file(args.config)
    
    # 创建生成器
    generator = ScaffoldGenerator(args.templates_dir)
    
    # 生成代码
    try:
        generated_files = generator.generate_from_config(config)
        
        print(f"成功生成 {len(generated_files)} 个文件:")
        for file_path in generated_files:
            print(f"  - {file_path}")
            
    except Exception as e:
        print(f"生成失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
